/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-02-26 14:57:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-14 10:15:38
 * @FilePath: /global-intelligence-web/vite.config.ts
 * @Description:
 */
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import path from 'path'
import vueJsx from '@vitejs/plugin-vue-jsx'
import VueDevTools from 'vite-plugin-vue-devtools'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import { compression } from 'vite-plugin-compression2'
import { visualizer } from 'rollup-plugin-visualizer'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  console.log('mode: ', mode)
  const isProduction = mode === 'production'

  return {
    plugins: [
      vue(),
      UnoCSS(),
      vueJsx(),
      VueDevTools(),
      Components({
        resolvers: [
          AntDesignVueResolver({
            importStyle: false // css in js
          })
        ]
      }),
      compression()
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@comp': path.resolve(__dirname, 'src/components'),
        '@api': path.resolve(__dirname, 'src/api'),
        '@assets': path.resolve(__dirname, 'src/assets')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json']
    },
    build: {
      reportCompressedSize: false,
      sourcemap: true, // 正式环境不生产sourcemap
      chunkSizeWarningLimit: 1024, // chunk 大小警告的限制（单位kb）
      minify: 'terser', // 启用 terser 压缩
      terserOptions: {
        compress: {
          drop_console: true, // 删除所有 console
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          experimentalMinChunkSize: 100 * 1024, // 最小chunk大小（单位b）
          // manualChunks(id) {
          //   // 创建一个对象映射，用于存储库名及其对应的chunk名称
          //   const libraryChunkMap: Record<string, string> = {
          //     lodash: 'lodash_es',
          //     'ant-design-vue': 'antd_vue',
          //     axios: 'axios',
          //     vue: 'vue',
          //     'ant-design+icons-svg': 'antd_icons',
          //     'crypto-js': 'crypto-js',
          //     dayjs: 'dayjs',
          //     echart: 'echart'
          //   }

          //   // 检查模块ID是否包含'node_modules'，即是否为第三方依赖
          //   if (id.includes('node_modules')) {
          //     const matchedLibrary = Object.keys(libraryChunkMap).find(library => id.includes(library))
          //     // 如果找到了匹配的库名，返回对应的chunk名称（从libraryChunkMap中获取）
          //     if (matchedLibrary) {
          //       return `${libraryChunkMap[matchedLibrary]}-vendor`
          //     } else {
          //       // 如果未找到匹配的库名，将该第三方依赖归入默认的'vendor' chunk
          //       return 'vendor'
          //     }
          //   }
          // },
          assetFileNames: 'assets/[ext]/[hash].[ext]',
          chunkFileNames: chunkInfo =>
            chunkInfo.name.includes('vendor') ? `assets/js/[name]-[hash].js` : 'assets/js/chunk-[hash].js',
          entryFileNames: chunkInfo =>
            chunkInfo.name.includes('vendor') ? `assets/js/[name]-[hash].js` : 'assets/js/chunk-[hash].js'
        },
        // plugins: [
        //   visualizer({
        //     open: true, // 直接在浏览器中打开分析报告
        //     filename: 'stats.html', // 输出文件的名称
        //     gzipSize: true, // 显示gzip后的大小
        //     brotliSize: true // 显示brotli压缩后的大小
        //   })
        // ]
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: true,
      proxy: {
        '/api_global_intelligence': {
          // target: 'http://************:21331', // 请求本地
          // target: 'http://localhost:21331',
          target: 'http://global.bengine.com.cn/api_global_intelligence/',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api_global_intelligence/, '/')
        },
        '/mock': {
          target: 'http://127.0.0.1:4523/m1/6011568-5700401-default/',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/mock/, '/')
        }
      }
    }
  }
})
