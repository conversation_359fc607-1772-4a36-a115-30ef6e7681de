<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-13 15:20:41
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 11:26:07
 * @FilePath: /global-intelligence-web/src/views/intelligence/components/groupList.vue
 * @Descripti
-->
<template>
  <div class="h100% flex flex-direction-column overflow-auto scrollbar-none scrollbar-w-4px">
    <div class="titleBox h62px p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">情报</p>

      <router-link to="/topic/index" class="color-#333">
        <iconfontIcon icon="icon-setting" />
      </router-link>
    </div>

    <ul class="px-8px">
      <li
        :class="['groupItem clickGroupItem', props.activityGroupId === '全部' ? 'bg-#f4f0ff' : '']"
        @click="handleTopicClick('全部')"
      >
        <p>
          <iconfontIcon icon="icon-format-horizontal-align-center"></iconfontIcon>
          全部
        </p>
      </li>

      <!-- :class="['groupItem clickGroupItem', props.activityGroupId === '全部' ? 'bg-#f4f0ff' : '']" -->
      <li class="groupItem">
        <p
          :class="['groupItem clickGroupItem', props.activityGroupId === '我的订阅' ? 'bg-#f4f0ff' : '']"
          @click="handleTopicClick('我的订阅')"
        >
          <iconfontIcon icon="icon-books"></iconfontIcon>
          我的订阅
        </p>
        <a-spin :spinning="loading">
          <div v-if="isEmpty(subscribeTopicList) || subscribeTopicList?.length === 0" class="pr-24px">
            <a-empty></a-empty>
          </div>
          <ul v-else class="subGroupList">
            <li
              v-for="(item, index) in subscribeTopicList"
              :key="index"
              :class="['groupItem clickGroupItem', props.activityGroupId === item.id ? 'bg-#f4f0ff' : '']"
              @click="handleTopicClick(item.id, item)"
            >
              <p class="flex-1">{{ item.topicName }}</p>
              <a-badge :count="item.unReadCount > 0 ? item.unReadCount : 0"> </a-badge>
            </li>
          </ul>
        </a-spin>
      </li>

      <!-- <li
        :class="['groupItem clickGroupItem', props.activityGroupId === '收藏' ? 'bg-#f4f0ff' : '']"
        @click="emits('update:activityGroupId', '收藏')"
      >
        <p>
          <iconfontIcon icon="icon-star"></iconfontIcon>
          我的收藏
        </p>
      </li> -->
    </ul>
  </div>
</template>

<script setup lang="ts">
import { newsTopicSubscribe } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import useRequest from '@/hooks/useRequest'
import { isEmpty, isNull } from 'lodash-es'
import type { NewsTopicSubscribeTopicResType } from '~/types/api/newsTopic/subscribe'

const props = defineProps<{ activityGroupId: string }>()
const emits = defineEmits(['update:activityGroupId', 'update:activeGroupInfo'])
const { dataList: subscribeTopicList, loading, getData } = useRequest(newsTopicSubscribe)

function handleTopicClick(id: string, info?: NewsTopicSubscribeTopicResType) {
  emits('update:activityGroupId', id)
  emits('update:activeGroupInfo', info)
}

function singleNewsReading(topicIds: string[] | null) {
  console.log('topicIds: ', topicIds)
  if (!isNull(topicIds)) {
    subscribeTopicList.value?.forEach(item => {
      if (topicIds.includes(item.id)) {
        item.unReadCount -= 1
      }
    })
  }
}

defineExpose({
  getData,
  singleNewsReading
})
</script>

<style lang="less" scoped>
.groupItem {
  @apply fs-16px color-#000-88 br-6px;
  + .groupItem {
    @apply mt-8px;
  }
  p {
    @apply transition-all ellipsis pl-16px py-6px;
  }
}

.subGroupList {
  @apply pl-2em pt-8px;
  .groupItem {
    @apply flex items-center pr-8px;
    p {
      padding-left: 8px;
    }
  }
}

.clickGroupItem {
  @apply cursor-pointer hover:(bg-#ebebeb);
}
</style>
