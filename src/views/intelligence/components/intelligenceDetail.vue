<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-10 11:40:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-23 10:25:35
 * @FilePath: /global-intelligence-web/src/views/intelligence/components/intelligenceDetail.vue
 * @Description: 
-->

<template>
  <div class="intelligenceDetail px32px">
    <div class="shareBtn py16px h62px flex-end-center">
      <a-space :size="16">
        <!-- <a-tooltip>
            <template #title>收藏</template>
            <a class="color-#999"><StarOutlined class="hoverPrimaryColor cursor-pointer fs-20px" /></a>
          </a-tooltip> -->
        <a-tooltip>
          <template #title>打开原文链接</template>
          <a class="color-#999" target="_blank" :href="intelligenceInfo.detailUrl">
            <CompassOutlined class="hoverPrimaryColor cursor-pointer fs-20px" />
          </a>
        </a-tooltip>
        <div v-if="isShowCloseBtn">
          <a @click="emits('close')">
            <CloseCircleOutlined class="fs-20px color-#999" />
          </a>
        </div>
        <!-- <a-tooltip>
            <template #title>分享</template>
            <a class="color-#999"><ExportOutlined class="hoverPrimaryColor cursor-pointer fs-20px" /></a>
          </a-tooltip> -->
      </a-space>
    </div>

    <!-- <a-space direction="vertical" :size="16"> -->
    <a-typography-title :level="1" class="color-#000-88 mb16px">
      {{ intelligenceInfo.translateTitle }}
    </a-typography-title>

    <div class="mb16px">
      <a-space>
        <a-typography-text type="secondary" v-if="intelligenceInfo.websiteName">
          {{ intelligenceInfo.websiteName }}
        </a-typography-text>
        <a-typography-text v-if="intelligenceInfo.websiteName !== intelligenceInfo.author" type="secondary">
          {{ intelligenceInfo.author }}
        </a-typography-text>
        <a-typography-text type="secondary">
          {{ publishTime }}
        </a-typography-text>
      </a-space>
    </div>

    <a-typography-text v-if="isAdminMode" type="secondary" class="mb16px">
      {{ intelligenceInfo.dataId }}
    </a-typography-text>

    <div class="mb16px">
      <a-tag v-for="tag in getTag('INDUSTRY_AFTER')" :key="tag" color="#6553ee" :bordered="false" class="mb-8px">
        {{ tag.label }}
      </a-tag>
      <a-tag v-for="tag in getTag('EVENT')" :key="tag" color="#D8F4F8" :bordered="false" class="mb-8px color-#595959!">
        {{ tag.label }}
      </a-tag>
      <a-tag
        v-for="tag in getTag('CONTINENT')"
        :key="tag"
        color="#FCEBE0"
        :bordered="false"
        class="mb-8px color-#595959!"
      >
        {{ tag.label }}
      </a-tag>
      <a-tag
        v-for="tag in getTag('COUNTRY')"
        :key="tag"
        color="#FCEBE0"
        :bordered="false"
        class="mb-8px color-#595959!"
      >
        {{ tag.label }}
      </a-tag>
      <a-tag
        v-for="tag in getTag('BRAND_AFTER')"
        :key="tag"
        color="#D6E5FD"
        :bordered="false"
        class="mb-8px color-#595959!"
      >
        {{ tag.label }}
      </a-tag>
      <a-tag v-for="tag in getTag('PRODUCT_TYPE')" :key="tag" :bordered="false" class="mb-8px color-#595959!">
        {{ tag.label }}
      </a-tag>
    </div>

    <div v-if="isAdminMode" class="mb16px">
      <div v-if="getTag('INDUSTRY_ORIGINAL').length !== 0">
        <span>行业原始标签：</span>
        <a-tag
          v-for="(tag, index) in getTag('INDUSTRY_ORIGINAL')"
          :key="index"
          color="orange"
          :bordered="false"
          class="mb-8px"
        >
          {{ tag.label }}
        </a-tag>
      </div>
      <div v-if="getTag('BRAND_ORIGINAL').length !== 0">
        <span>品牌原始标签：</span>
        <a-tag
          v-for="(tag, index) in getTag('BRAND_ORIGINAL')"
          :key="index"
          color="orange"
          :bordered="false"
          class="mb-8px"
        >
          {{ tag.label }}
        </a-tag>
      </div>
    </div>

    <div class="flex-start-start bg-#FFFAEB p8px br-4px mb16px">
      <img src="@/assets/ai_abstract.svg" class="mr-4px" />
      <div class="color-#222 fs-16">
        <strong class="fw-400">AI摘要：</strong>
        <span class="color-#000-65">
          {{ intelligenceInfo.translateSummary }}
        </span>
      </div>
    </div>

    <div class="contentTextText mb16px" v-html="contentText"></div>

    <div class="originTextContent mb16px" v-if="!intelligenceInfo.isZh">
      <p v-if="!showOriginText" class="fs-16px color-#999 hoverPrimaryColor" @click="showOriginText = !showOriginText">
        译文原文
      </p>
      <template v-if="showOriginText">
        <a-divider />
        <div class="contentTextText" v-html="originText"></div>
        <p class="fs-16px color-#999 hoverPrimaryColor" @click="showOriginText = !showOriginText">收起原文</p>
      </template>
    </div>

    <a-divider />

    <!-- <div class="flex-center-center">
        <a-space>
          <a-button type="primary">收藏</a-button>
          <a-button>分享</a-button>
        </a-space>
      </div> -->
    <!-- </a-space> -->
  </div>
</template>

<script setup lang="ts">
import { CloseCircleOutlined, CompassOutlined } from '@ant-design/icons-vue'
import { has, isArray, isUndefined } from 'lodash-es'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import type { newsPageResType } from '~/types/api/news/page'
import MarkdownIt from 'markdown-it'

const route = useRoute()
const { admin } = route.query
const isAdminMode = computed(() => !isUndefined(admin) && admin === '1')

const emits = defineEmits(['close'])
const props = withDefaults(
  defineProps<{
    intelligenceInfo: newsPageResType
    isShowCloseBtn?: boolean
  }>(),
  {
    isShowCloseBtn: false
  }
)

const markdown = MarkdownIt({
  breaks: true,
  html: true,
  linkify: true
})
// 自定义规则
// 图片只保留src和style属性
markdown.renderer.rules.image = function (tokens, idx, options, env, self) {
  const token = tokens[idx]
  const src = token.attrGet('src')
  const style = token.attrGet('style')
  return `<img src="${src}" style="${style}" />`
}
// 链接在新页签中打开
markdown.renderer.rules.link_open = function (tokens, idx, options, _env, self) {
  // 添加 target="_blank" 属性
  tokens[idx].attrPush(['target', '_blank'])
  tokens[idx].attrPush(['rel', 'noopener noreferrer'])
  // 超链接hover样式给个下划线
  tokens[idx].attrPush(['class', 'linkHover'])
  // 渲染标签
  return self.renderToken(tokens, idx, options)
}

const contentText = computed(() => {
  const text = props.intelligenceInfo.translateContent.replace(/\*\*/g, '\u200B**\u200B') // 添加零宽空格 https://juejin.cn/post/7064565848421171213
  return markdown.render(text)
})

// 显示原文
const showOriginText = ref(false)
const originText = computed(() => {
  const text = props.intelligenceInfo.content
  return `<div style="margin-bottom:16px">${'<span class="color-#222">原文内容：</span>'}</div>` + markdown.render(text)
})

const getTag = (key: string) => {
  const tempList = props.intelligenceInfo.tagMap[key]
  if (has(props.intelligenceInfo.tagMap, key) && isArray(tempList)) {
    return tempList.map(item => ({ label: item, type: key }))
  }
  return []
}

const publishTime = computed(() => {
  if (!dayjs(props.intelligenceInfo.publishTime).isValid()) return ''
  const date = dayjs(props.intelligenceInfo.publishTime)
  if (date.hour() === 0 && date.minute() === 0 && date.second() === 0) {
    return date.format('YYYY-MM-DD')
  } else {
    return date.format('YYYY-MM-DD HH:mm:ss')
  }
})
</script>

<style lang="less" scoped>
.intelligenceDetail {
  word-wrap: break-word;
  ::v-deep(.contentTextText) {
    @apply 'lh-2em color-#222 fs-18';
    & > * {
      margin-bottom: 16px !important;
    }
    pre,
    code {
      // display: block;
      // white-space: pre-wrap; /* 保留空白符但允许换行 */
      // word-break: break-all; /* 强制所有字符都可以换行 */
      // overflow-x: auto; /* 必要时显示水平滚动条 */

      white-space: pre-wrap !important;
      word-break: break-all !important;
      overflow-wrap: anywhere !important;
      display: block !important;
    }

    tr > td {
      border: 1px #dee2e5 solid;
      padding: 8px;
    }

    .linkHover {
      // 超链接hover样式给个下划线。
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
