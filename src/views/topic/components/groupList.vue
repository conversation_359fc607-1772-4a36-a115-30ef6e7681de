<template>
  <div class="h100% flex flex-direction-column">
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">订阅</p>
    </div>

    <ul class="px-8px">
      <li
        :class="['groupItem clickGroupItem', props.activityGroupId === '全部' ? 'bg-#f4f0ff' : '']"
        @click="emits('update:activityGroupId', '全部')"
      >
        <p>
          <iconfontIcon icon="icon-format-horizontal-align-center"></iconfontIcon>
          全部
        </p>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'

const props = defineProps<{ activityGroupId: string }>()
const emits = defineEmits(['update:activityGroupId'])
</script>

<style lang="less" scoped>
.groupItem {
  @apply fs-16px color-#000-88 br-6px;
  + .groupItem {
    @apply mt-8px;
  }
  p {
    @apply transition-all ellipsis pl-16px py-6px;
  }
}

.subGroupList {
  @apply pl-2em pt-8px;
  .groupItem {
    @apply flex items-center pr-8px;
    p {
      padding-left: 8px;
    }
  }
}

.clickGroupItem {
  @apply cursor-pointer hover:(bg-#ebebeb);
}
</style>
