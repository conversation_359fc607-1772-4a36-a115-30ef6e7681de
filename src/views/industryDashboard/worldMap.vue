<template>
  <a-card title="区域动态" :bodyStyle="{ height: '100%', overflow: 'hidden' }">
    <div class="w100% h100%" ref="worldMapRef">
      <a-spin :spinning="loading">
        <img
          ref="worldMapImgRef"
          src="@/assets/worldMap.png"
          class="h100% mx-auto object-fit-contain"
          @load="getMarginLeft"
        />
        <div class="h100% absolute t-0" :style="{ width: imgWidth, left: imgMarginLeft }">
          <div class="w100% h100% relative">
            <div
              v-for="(item, index) in dataList?.filter(item => ['亚洲', '欧洲', '北美洲', '南美洲', '非洲', '大洋洲'].includes(item.continent!))"
              :key="index"
              :class="{
                tipsBox: true,
                asia: item.continent === '亚洲',
                europe: item.continent === '欧洲',
                northAmerica: item.continent === '北美洲',
                'southAmerica ': item.continent === '南美洲',
                'africa ': item.continent === '非洲',
                'oceania ': item.continent === '大洋洲'
              }"
            >
              <div class="circlePoint"></div>

              <div class="newsContent">
                <p class="title">{{ item.continent }}</p>
                <div class="flex-1 scrollbar-none overflow-auto">
                  <div
                    v-for="newsItem in item.newsList"
                    :key="newsItem.dataId"
                    class="hoverPrimaryColor newsItem flex-start-start"
                    @click="emits('click', newsItem)"
                  >
                    <span class="circle w12px h12px bg-#000-15 block mt-5px mr-4px"></span>
                    <p class="flex-1 ellipsis-2">{{ newsItem.title }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { informationBoardRegionalDynamics } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import { useElementSize } from '@vueuse/core'
import { computed, ref, useTemplateRef, watch } from 'vue'
import type { regionalDynamicsReqType } from '~/types/api/informationBoard/regionalDynamics'

const props = defineProps<{ industryId: string }>()
const emits = defineEmits(['click'])

const params = computed<regionalDynamicsReqType>(() => ({ industryId: props.industryId, size: 10 }))
const { dataList, loading, getData } = useRequest(informationBoardRegionalDynamics, params, {
  immediateReqData: false
})

const worldMapRef = useTemplateRef('worldMapRef')
const worldMapImgRef = useTemplateRef('worldMapImgRef')

const imgMarginLeft = ref('0px')
const imgWidth = ref('0px')
// 图片加载完成后获取左边边距
function getMarginLeft() {
  const img = worldMapImgRef.value
  const styleList = window.getComputedStyle(img as Element)
  imgMarginLeft.value = styleList.marginLeft
  imgWidth.value = styleList.width
}

const { width, height } = useElementSize(worldMapRef)
watch([width, height], () => {
  getMarginLeft()
})

defineExpose({
  getDynamicsData: getData
})
</script>

<style lang="less" scoped>
@tipsBorderColor: #6553ee;

.tipsBox {
  @apply absolute flex-center-center;

  .circlePoint {
    position: relative;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: @tipsBorderColor;

    &::before {
      content: '';
      position: absolute;
      display: block;
      width: 8px;
      height: 1px;
      background-color: @tipsBorderColor;
      right: -5px;
      top: 8px;
    }
  }
  .newsContent {
    margin-left: 4px;
    border-radius: 4px;
    border: 1px solid @tipsBorderColor;
    width: 200px;
    height: 230px;
    backdrop-filter: blur(3px);
    background-color: rgba(255, 255, 255, 80%);
    display: flex;
    flex-direction: column;
    .title {
      padding: 4px;
      @apply fs-14px fw-500;
      border-bottom: 1px solid #f0f0f0;
    }
    .newsItem {
      padding: 4px 4px 0;
      // + .newsItem {
      //   margin-top: 4px;
      // }
      &:last-child {
        padding-bottom: 4px;
      }
    }
  }
}

.reverse {
  flex-direction: row-reverse;
  .circlePoint {
    &::before {
      right: 0px;
      left: -5px;
    }
  }
  .newsContent {
    margin-right: 4px;
  }
}

.asia {
  left: 72%;
  top: 6%;
}
.europe {
  left: 42%;
  top: 6%;
}
.northAmerica {
  left: 8%;
  top: 6%;
}

.southAmerica {
  left: 16%;
  top: 52%;
}
.africa {
  left: 45%;
  top: 52%;
}
.oceania {
  left: 75%;
  top: 52%;
}

@media (min-width: 1600px) {
  .southAmerica {
    top: 58%;
  }
  .africa {
    top: 58%;
  }
  .oceania {
    left: 77%;
    top: 58%;
  }
}
</style>
