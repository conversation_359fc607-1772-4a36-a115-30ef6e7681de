<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:20:39
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-14 10:35:49
 * @FilePath: /global-intelligence-web/src/views/task/index.vue
 * @Description: 
-->
<template>
  <div class="flex h-full">
    <div class="min-w-260px max-w-260px h100% border-c-#050505-6 border-r-1px border-solid">
      <Group />
    </div>
    <div class="flex-1 p16px">
      <ChatIndex />
    </div>
  </div>
</template>

<script setup lang="ts">
import Group from './components/group.vue'
import ChatIndex from './components/chat/index.vue'
</script>

<style scoped></style>
