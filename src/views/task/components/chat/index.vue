<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:36:13
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-15 15:54:41
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/index.vue
 * @Description: 
-->
<template>
  <div>
    <div class="max-w800px mx-auto">
      <div class="border-[0.5px] w-full p-16px box-shadow relative rounded-2xl">
        <Editor :agentPrompt="agentPrompt" />
        <!-- v-model="value" -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Editor from './editor.vue'

const agentPrompt =
  '我公司是' +
  '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
  '，我负责销售' +
  '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
  '，正在寻找' +
  '{"type":"InputSlot","props":{"placeholder":"国家"}}' +
  '和' +
  '{"type":"InputSlot","props":{"placeholder":"行业"}}' +
  '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'

const value = ref('')
</script>

<style scoped></style>
