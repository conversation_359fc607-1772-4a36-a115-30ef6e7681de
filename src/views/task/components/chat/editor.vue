<template>
  <div class="max-h222px scroller">
    <div
      class="content"
      :spellcheck="false"
      autocorrect="off"
      autocapitalize="off"
      translate="no"
      contenteditable="true"
      role="textbox"
      aria-multiline="true"
      ref="contentRef"
      :data-placeholder="placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
      @click="handleClick"
    >
      <template v-for="item in content" :key="item.id">
        <template v-if="item.type === 'text'">
          {{ item.value }}
        </template>
        <AgentInput
          v-else-if="item.type === 'InputSlot'"
          :value="item.value"
          :placeholder="item.props?.placeholder"
          :slot-data="item"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AgentInput from './agentInput/index.vue'
import { randomUUID } from '@/utils/util'
import { isNull } from 'lodash-es'

export interface PromptItemType {
  type: string
  id: string
  value: string
  props?: { placeholder: string; options?: string[] }
}

interface Props {
  placeholder?: string
  agentPrompt?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '给我布置一个任务（可以让我寻找海外商机、解析海外国家政策、跟踪海外行业新闻...）',
  agentPrompt: ''
})

const contentRef = ref<HTMLDivElement>()
const hasContent = ref(false)
const content = ref<PromptItemType[]>([])
let editKey = ''

// 解析agentPrompt内容，生成组件
function parseAgentPrompt(text: string): PromptItemType[] {
  text = text.replace(/\n/g, '')
  const result: PromptItemType[] = []
  let lastIndex = 0

  // 匹配 {"type": ...} 这样的 JSON 结构
  const regex = /(\{"type"[^{}]*(?:\{[^{}]*\}[^{}]*)*\})/g

  let match
  while ((match = regex.exec(text)) !== null) {
    const [fullMatch] = match

    // 提取前面的文本
    const precedingText = text.slice(lastIndex, match.index)
    if (precedingText) {
      result.push({ type: 'text', value: precedingText, id: randomUUID() })
    }

    // 尝试解析 JSON
    try {
      const json = JSON.parse(fullMatch)

      // 如果有 options 字段，转成数组
      if (json.props?.options && typeof json.props.options === 'string') {
        json.props.options = json.props.options.split(',')
      }

      result.push({ ...json, value: json.value ? json.value : '', id: randomUUID() })
    } catch (e) {
      console.warn('JSON 解析失败:', fullMatch)
      result.push({ type: 'text', value: fullMatch, id: randomUUID() })
    }

    lastIndex = regex.lastIndex
  }

  // 添加最后剩余的文本
  const remainingText = text.slice(lastIndex)
  if (remainingText) {
    result.push({ type: 'text', value: remainingText, id: randomUUID() })
  }

  return result
}

// 处理空内容，为了防止删除所有内容后会自动生成br，阻碍了placeholder的显示
const forceClearIfEmpty = () => {
  if (!contentRef.value) return

  const html = contentRef.value.innerHTML
  const isBrOnly = html === '<br>' || html === '<br/>'
  const isEmpty = html.trim() === ''

  if (isBrOnly || isEmpty) {
    contentRef.value.innerHTML = ''
    hasContent.value = false
  }
}

onMounted(() => {
  content.value = parseAgentPrompt(props.agentPrompt)
})

function handleInput(e: Event) {
  console.log('e: ', e)
  console.log('editKey: ', editKey)
  if (editKey) {
    console.log('contentRef.value?.innerHTML: ', contentRef.value)
  }
}

function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    e.preventDefault()
    console.log('回车触发', contentRef.value?.innerText)
  }
}

function handleFocus() {
  // 不做任何操作，保持placeholder显示
}

function handleBlur() {
  // forceClearIfEmpty()
}

function handleClick(e: MouseEvent) {
  const target = e.target as HTMLElement
  // 如果点击的是agentInput或者placeholder，就获取光标
  if (!(target.className.includes('agentInput') || target.className.includes('placeholder'))) {
    editKey = ''
    return
  }
  // 判断是不是agentInput节点
  const isAgentInputNode = target.className.includes('agentInput')
  // 获取agentInput节点
  const agentInputNode = isAgentInputNode ? target : target.parentElement
  if (isNull(agentInputNode)) {
    return
  }
  const dataKey = agentInputNode.getAttribute('data-key')
  editKey = dataKey || ''
  const editIndex = content.value.findIndex(item => item.id === dataKey)
  // 判断node内有没有文本，没有文本就设置光标位置，有就不设置
  if (agentInputNode.innerText.trim() !== content.value[editIndex].props?.placeholder) {
    return
  }
  // 获取光标显示位置的node
  const rangeTarget = agentInputNode.querySelector('.slot-side-left')
  if (isNull(rangeTarget)) {
    return
  }
  // 创建一个新的范围
  const range = document.createRange()
  const selection = window.getSelection()
  // 将光标设置在agentInput节点的开始位置
  range.setStartAfter(rangeTarget)
  range.collapse(true)

  // 应用选择
  selection?.removeAllRanges()
  selection?.addRange(range)
}
</script>

<style lang="less" scoped>
.scroller {
  display: flex !important;
  align-items: flex-start !important;
  font-family: monospace;
  line-height: 1.4;
  height: 100%;
  overflow-x: auto;
  position: relative;
  z-index: 0;
  overflow-anchor: none;
}

.content {
  @apply min-h62px cursor-text fs-16px;
  outline: none;
  width: 100%;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
  tab-size: 2;
  line-height: 30px;

  .textWarper {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: row;
  }

  &[data-placeholder]:not(.has-content) {
    &:empty,
    &:has(br:only-child) {
      &:before {
        content: attr(data-placeholder);
        color: #999;
        pointer-events: none;
      }
    }
  }
}
</style>
