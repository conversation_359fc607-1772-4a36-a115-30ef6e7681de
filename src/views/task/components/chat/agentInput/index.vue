<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 11:53:10
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/agentInput/index.vue
 * @Description: 
-->
<template>
  <div class="agentInput" :data-key="slotData.id">
    <img class="widgetBuffer" aria-hidden="true" />
    <span class="slot-side-left" contenteditable="false"></span>
    <span
      class="input-content"
      :class="{ 'show-placeholder': !hasContent }"
      contenteditable="true"
      :data-placeholder="placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
      @paste="handlePaste"
      ref="inputRef"
    ></span>
    <span class="slot-side-right" contenteditable="false"></span>
    <img class="widgetBuffer" aria-hidden="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { PromptItemType } from '../editor.vue'

interface Props {
  value?: string
  placeholder?: string
  slotData: PromptItemType
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:value': [value: string]
  'input': [value: string, slotData: PromptItemType]
  'focus': [slotData: PromptItemType]
  'blur': [slotData: PromptItemType]
}>()

const inputRef = ref<HTMLElement>()
const modelValue = computed({
  get: () => props.value || '',
  set: (val: string) => emit('update:value', val)
})

const hasContent = computed(() => {
  return modelValue.value.trim() !== ''
})

function handleInput(e: Event) {
  const target = e.target as HTMLElement
  const value = target.innerText || ''
  emit('update:value', value)
  emit('input', value, props.slotData)
}

function handleFocus() {
  emit('focus', props.slotData)
}

function handleBlur() {
  emit('blur', props.slotData)
}

// 监听 value 变化，同步到 DOM
watch(() => props.value, (newValue) => {
  if (inputRef.value && inputRef.value.innerText !== newValue) {
    inputRef.value.innerText = newValue || ''
  }
}, { immediate: true })

function handleKeydown(e: KeyboardEvent) {
  // 阻止某些键的默认行为，让父组件处理导航
  if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
    const selection = window.getSelection()
    if (!selection || !inputRef.value) return

    const range = selection.getRangeAt(0)
    const text = inputRef.value.innerText || ''
    const cursorPosition = range.startOffset

    // 如果在边界位置，让事件冒泡到父组件处理
    if ((e.key === 'ArrowLeft' && cursorPosition === 0) ||
        (e.key === 'ArrowRight' && cursorPosition >= text.length)) {
      // 不阻止默认行为，让父组件处理
      return
    }
  }
}

function handlePaste(e: ClipboardEvent) {
  // 阻止默认粘贴行为
  e.preventDefault()

  // 获取剪贴板数据
  const clipboardData = e.clipboardData
  if (!clipboardData) return

  // 只获取纯文本内容
  const plainText = clipboardData.getData('text/plain')
  if (!plainText) return

  // 清理文本：移除换行符和多余空白，因为 agentInput 是单行输入
  const cleanText = plainText
    .replace(/[\r\n]/g, ' ')  // 将换行符替换为空格
    .replace(/\s+/g, ' ')     // 将多个空白字符合并为单个空格
    .trim()                   // 移除首尾空白

  if (!cleanText) return

  // 插入纯文本到当前光标位置
  insertTextAtCursor(cleanText)
}

// 在光标位置插入文本
function insertTextAtCursor(text: string) {
  if (!inputRef.value) return

  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)

  // 删除选中的内容（如果有）
  range.deleteContents()

  // 创建文本节点并插入
  const textNode = document.createTextNode(text)
  range.insertNode(textNode)

  // 设置光标到插入内容的末尾
  range.setStartAfter(textNode)
  range.collapse(true)
  selection.removeAllRanges()
  selection.addRange(range)

  // 触发输入事件以更新状态
  const inputEvent = new Event('input', { bubbles: true })
  inputRef.value.dispatchEvent(inputEvent)
}
</script>

<style scoped lang="less">
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

.agentInput {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
}

.input-content {
  outline: none;
  min-width: 1px;
  color: #6553ee;

  &.show-placeholder:empty::before {
    content: attr(data-placeholder);
    color: #f4f0ff;
    pointer-events: none;
  }
}

.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}

// .slot-side-left {
//   border-radius: @slotRadius 0 0 @slotRadius;
//   padding: @slotPaddingY 0 @slotPaddingY @slotPaddingX;
//   background: @slotBgColor;
//   margin-left: @slotMargin;
// }
// .slot-side-right {
//   border-radius: 0 @slotRadius @slotRadius 0;
//   padding: @slotPaddingY @slotPaddingX @slotPaddingY 0;
//   background: @slotBgColor;
//   margin-right: @slotMargin;
// }

// .inputSlot {
//   padding: @slotPaddingY 0;
// }
</style>
