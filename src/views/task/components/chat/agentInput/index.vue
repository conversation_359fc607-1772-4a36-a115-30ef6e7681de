<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 11:53:10
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/agentInput/index.vue
 * @Description: 
-->
<template>
  <div class="agentInput" :data-key="slotData.id">
    <img class="widgetBuffer" aria-hidden="true" />
    <span class="slot-side-left" contenteditable="false"></span>
    <span
      class="input-content"
      :class="{ 'show-placeholder': !hasContent }"
      contenteditable="true"
      :data-placeholder="placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      ref="inputRef"
    >{{ modelValue }}</span>
    <span class="slot-side-right" contenteditable="false"></span>
    <img class="widgetBuffer" aria-hidden="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { PromptItemType } from '../editor.vue'

interface Props {
  value?: string
  placeholder?: string
  slotData: PromptItemType
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:value': [value: string]
  'input': [value: string, slotData: PromptItemType]
  'focus': [slotData: PromptItemType]
  'blur': [slotData: PromptItemType]
}>()

const inputRef = ref<HTMLElement>()
const modelValue = computed({
  get: () => props.value || '',
  set: (val: string) => emit('update:value', val)
})

const hasContent = computed(() => {
  return modelValue.value.trim() !== ''
})

function handleInput(e: Event) {
  const target = e.target as HTMLElement
  const value = target.innerText || ''
  emit('update:value', value)
  emit('input', value, props.slotData)
}

function handleFocus() {
  emit('focus', props.slotData)
}

function handleBlur() {
  emit('blur', props.slotData)
}

// 监听 value 变化，同步到 DOM
watch(() => props.value, (newValue) => {
  if (inputRef.value && inputRef.value.innerText !== newValue) {
    inputRef.value.innerText = newValue || ''
  }
}, { immediate: true })
</script>

<style scoped lang="less">
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

.agentInput {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
}

.input-content {
  outline: none;
  min-width: 1px;
  color: #6553ee;

  &.show-placeholder:empty::before {
    content: attr(data-placeholder);
    color: #f4f0ff;
    pointer-events: none;
  }
}

.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}

// .slot-side-left {
//   border-radius: @slotRadius 0 0 @slotRadius;
//   padding: @slotPaddingY 0 @slotPaddingY @slotPaddingX;
//   background: @slotBgColor;
//   margin-left: @slotMargin;
// }
// .slot-side-right {
//   border-radius: 0 @slotRadius @slotRadius 0;
//   padding: @slotPaddingY @slotPaddingX @slotPaddingY 0;
//   background: @slotBgColor;
//   margin-right: @slotMargin;
// }

// .inputSlot {
//   padding: @slotPaddingY 0;
// }
</style>
