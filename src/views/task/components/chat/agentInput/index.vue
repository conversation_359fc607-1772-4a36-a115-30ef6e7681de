<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 11:59:19
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/agentInput/index.vue
 * @Description: 
-->
<template>
  <div class="agentInput" :data-key="slotData.id">
    <img class="widgetBuffer" aria-hidden="true" />
    <span class="slot-side-left" contenteditable="false"></span>
    <span
      class="input-content"
      :class="{ 'show-placeholder': !hasContent }"
      contenteditable="true"
      :data-placeholder="placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      ref="inputRef"
    >{{ modelValue }}</span>
    <span class="slot-side-right" contenteditable="false"></span>
    <img class="widgetBuffer" aria-hidden="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { PromptItemType } from '../editor.vue'

interface Props {
  value?: string
  placeholder?: string
  slotData: PromptItemType
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:value': [value: string]
  'input': [value: string, slotData: PromptItemType]
  'focus': [slotData: PromptItemType]
  'blur': [slotData: PromptItemType]
}>()

const inputRef = ref<HTMLElement>()
const currentValue = ref('')

const modelValue = computed({
  get: () => props.value || '',
  set: (val: string) => emit('update:value', val)
})

const hasContent = computed(() => {
  return currentValue.value.trim() !== ''
})

function handleInput(e: Event) {
  const target = e.target as HTMLElement
  let value = target.innerText || ''

  // 处理空内容的情况，清除可能存在的 br 标签
  if (value.trim() === '' || value === '\n') {
    value = ''
    target.innerHTML = ''
  }

  currentValue.value = value
  emit('update:value', value)
  emit('input', value, props.slotData)
}

function handleFocus() {
  emit('focus', props.slotData)
}

function handleBlur() {
  // 失焦时也检查并清理空内容
  if (inputRef.value) {
    const value = inputRef.value.innerText || ''
    if (value.trim() === '' || value === '\n') {
      inputRef.value.innerHTML = ''
      currentValue.value = ''
      emit('update:value', '')
      emit('input', '', props.slotData)
    }
  }
  emit('blur', props.slotData)
}

// 监听 value 变化，同步到 DOM 和 currentValue
watch(() => props.value, (newValue) => {
  const value = newValue || ''
  currentValue.value = value
  if (inputRef.value && inputRef.value.innerText !== value) {
    inputRef.value.innerText = value
  }
}, { immediate: true })

// 监听 DOM 变化，处理用户直接操作 DOM 的情况
watch(inputRef, (el) => {
  if (el) {
    // 初始化 currentValue
    currentValue.value = el.innerText || ''
  }
}, { immediate: true })
</script>

<style scoped lang="less">
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

.agentInput {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
}

.input-content {
  outline: none;
  min-width: 1px;
  color: #6553ee;

  &.show-placeholder::before {
    content: attr(data-placeholder);
    color: #f4f0ff;
    pointer-events: none;
  }

  // 确保在空内容时显示 placeholder
  &.show-placeholder:empty::before,
  &.show-placeholder:has(br:only-child)::before {
    content: attr(data-placeholder);
    color: #f4f0ff;
    pointer-events: none;
  }
}

.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}

// .slot-side-left {
//   border-radius: @slotRadius 0 0 @slotRadius;
//   padding: @slotPaddingY 0 @slotPaddingY @slotPaddingX;
//   background: @slotBgColor;
//   margin-left: @slotMargin;
// }
// .slot-side-right {
//   border-radius: 0 @slotRadius @slotRadius 0;
//   padding: @slotPaddingY @slotPaddingX @slotPaddingY 0;
//   background: @slotBgColor;
//   margin-right: @slotMargin;
// }

// .inputSlot {
//   padding: @slotPaddingY 0;
// }
</style>
