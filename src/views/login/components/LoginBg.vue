<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-05 11:54:14
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-11 15:57:33
 * @FilePath: /global-intelligence-web/src/views/login/components/LoginBg.vue
 * @Description: 
-->
<template>
  <div class="left-container">
    <img src="@/assets/company_logo_white.svg" class="logo" />

    <div class="slide-container">
      <div class="slide-item">
        <h1>驭见全球情报，伴同扬帆远航</h1>
        <div>
          <p>实时汇聚全球权威信息源，智能提取产业动态、市场变化与潜在风险，助力企业做出更快更准的战略判断</p>
          <p>无论是研发前沿、供应链风险，还是竞争格局演化，用户皆可通过平台抢先掌握，布局未来</p>
        </div>
        <img src="@/assets/<EMAIL>" />
      </div>
    </div>

    <!-- <swiper
      :modules="[Pagination, Autoplay]"
      :autoplay="{ delay: 5000 }"
      :speed="600"
      :loop="true"
      :pagination="{ el: '.pagination', type: 'bullets' }"
    >
      <swiper-slide class="swiper-slide" v-for="(item, index) in bgList" :key="index">
        
      </swiper-slide>
    </swiper> -->

    <!-- <div class="pagination"></div> -->

    <div class="bottomInfo">Powered by Busiengine</div>
  </div>
</template>

<script setup lang="ts"></script>

<style lang="less" scoped>
.left-container {
  background: linear-gradient(149deg, #0a376e 0%, #041427 100%);
  @apply overflow-hidden relative flex-1 flex flex-direction-column;
  width: 100%;
  height: 100%;
  // .logo {
  //   position: absolute;
  //   top: 40px;
  //   left: 60px;
  //   width: 10vw;
  //   min-width: 160px;
  //   z-index: 99;
  // }

  .bottomInfo {
    width: 100%;
    font-size: 12px;
    color: #ffffff;
    opacity: 0.6;
    position: absolute;
    bottom: 2.78vh;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    text-align: center;
  }
}

.left-container {
  --swiper-theme-color: #fff;
  --swiper-pagination-color: #fff; /* 两种都可以 */
  --swiper-pagination-bullet-inactive-color: rgba(255, 255, 255, 0.7);
  --swiper-pagination-bullet-width: 6px;
  --swiper-pagination-bullet-height: 6px;

  background: linear-gradient(149deg, #0a376e 0%, #041427 100%);
  overflow: hidden;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  .logo {
    position: absolute;
    top: 40px;
    left: 60px;
    width: 10vw;
    min-width: 160px;
    z-index: 99;
  }

  .slide-container {
    width: 100%;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;

    .slide-item {
      color: #ffffff;
      text-align: center;
      width: 60vw;
      min-width: 825px;
      margin: 0 auto;
      cursor: default;

      h1 {
        color: #ffffff;
        opacity: 1;
        margin: 0 auto;

        cursor: default;
        font-size: 2.6vw;
        font-weight: 550;
        margin-bottom: 1.1vh;
      }

      div {
        margin: 8px auto 0;
        cursor: default;
        // width: 30vw;
        min-width: 440px;

        p {
          color: #e5e5e5;
          font-size: 0.83vw;
          opacity: 0.9;
          // height: 2.7vh;
          // line-height: 2.7vh;
        }
      }

      img {
        min-height: 470px;
        height: 60vh;
        min-width: 730px;
        width: 90%;
        object-fit: contain;
      }
    }

    @media screen and (max-height: 750px) {
      .slide-item {
        img {
          margin-top: 0;
        }
      }
    }
    @media screen and (max-height: 900px) {
      .slide-item {
        h1 {
          margin-top: 30px;
        }
      }
    }
    @media screen and (max-width: 1280px) {
      .slide-item {
        min-width: 730px;
      }
      img {
        max-width: 730px;
      }
    }
    @media screen and (max-width: 1690px) {
      .slide-item {
        h1 {
          font-size: 32px;
          // margin-top: 120px;
        }
        div {
          p {
            font-size: 14px;
          }
        }
      }
    }
  }

  .pagination {
    padding-bottom: 6vh;
    align-items: center;
    display: flex;
    justify-content: center;
    position: absolute;
  }

  .bottomInfo {
    width: 100%;
    font-size: 12px;
    color: #ffffff;
    opacity: 0.6;
    position: absolute;
    bottom: 2.78vh;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    text-align: center;
  }
}
</style>
