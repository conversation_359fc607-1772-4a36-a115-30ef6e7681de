{"name": "global-intelligence-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "tscBuild": "vue-tsc -b && vite build --mode production", "tsc": "vue-tsc -b", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/components": "^12.7.0", "@vueuse/core": "^12.7.0", "ant-design-vue": "^4.2.6", "axios": "^1.8.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "nprogress": "^0.2.0", "pinia": "^3.0.1", "qs": "^6.14.0", "unocss-preset-scalpel": "^1.2.7", "unocss-preset-scrollbar": "^3.2.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vue3-text-clamp": "^0.1.2"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^22.13.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "^0.7.0", "less": "^4.2.2", "rollup-plugin-visualizer": "^5.14.0", "terser": "^5.39.0", "typescript": "~5.7.2", "unocss": "0.65.4", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vite-plugin-compression2": "^1.3.3", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.4"}}